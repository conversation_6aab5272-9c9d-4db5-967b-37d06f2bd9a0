-- 录音转写系统数据库设计
-- 包含用户管理、第三方登录、时长管理、订单管理功能

-- 1. 用户基础信息表
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE COMMENT '用户名',
    email VARCHAR(100) UNIQUE COMMENT '邮箱',
    phone VARCHAR(20) UNIQUE COMMENT '手机号',
    password_hash VARCHAR(255) COMMENT '密码哈希',
    nickname VARCHAR(100) COMMENT '昵称',
    avatar VARCHAR(500) COMMENT '头像URL',
    activation_code VARCHAR(20) NOT NULL COMMENT '激活码（必填）',
    status TINYINT DEFAULT 1 COMMENT '用户状态：1-正常，0-禁用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_phone (phone),
    INDEX idx_activation_code (activation_code),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户基础信息表';

-- 2. 第三方登录表
CREATE TABLE user_third_party (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    provider VARCHAR(20) NOT NULL COMMENT '第三方平台：wechat, qq, weibo等',
    provider_user_id VARCHAR(100) NOT NULL COMMENT '第三方平台用户ID',
    provider_data JSON COMMENT '第三方平台返回的用户信息',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_provider_user (provider, provider_user_id),
    INDEX idx_user_id (user_id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='第三方登录表';

-- 3. 用户时长管理表
CREATE TABLE user_duration (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    total_duration BIGINT DEFAULT 0 COMMENT '总时长（秒）',
    used_duration BIGINT DEFAULT 0 COMMENT '已使用时长（秒）',
    remaining_duration BIGINT DEFAULT 0 COMMENT '剩余时长（秒）',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_user_id (user_id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户时长管理表';

-- 4. 转写记录表
CREATE TABLE transcription_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    file_name VARCHAR(255) NOT NULL COMMENT '文件名',
    file_size BIGINT COMMENT '文件大小（字节）',
    file_url VARCHAR(500) COMMENT '文件存储URL',
    duration_used BIGINT NOT NULL COMMENT '本次使用时长（秒）',
    status TINYINT DEFAULT 0 COMMENT '转写状态：0-处理中，1-成功，2-失败',
    result_text LONGTEXT COMMENT '转写结果文本',
    error_message TEXT COMMENT '错误信息',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='转写记录表';

-- 5. 订单表
CREATE TABLE orders (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    order_no VARCHAR(32) UNIQUE NOT NULL COMMENT '订单号',
    total_amount BIGINT NOT NULL COMMENT '订单总金额（分）',
    status TINYINT DEFAULT 0 COMMENT '订单状态：0-待支付，1-已支付，2-已取消，3-已退款',
    payment_method VARCHAR(20) COMMENT '支付方式：wechat, alipay等',
    payment_no VARCHAR(100) COMMENT '第三方支付订单号',
    paid_at TIMESTAMP NULL COMMENT '支付时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_order_no (order_no),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单表';

-- 6. 订单项表
CREATE TABLE order_items (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    order_id BIGINT NOT NULL COMMENT '订单ID',
    product_type VARCHAR(20) NOT NULL COMMENT '产品类型：duration_package',
    product_name VARCHAR(100) NOT NULL COMMENT '产品名称',
    duration_minutes BIGINT NOT NULL COMMENT '购买的时长（分钟）',
    unit_price BIGINT NOT NULL COMMENT '单价（分）',
    quantity INT DEFAULT 1 COMMENT '数量',
    amount BIGINT NOT NULL COMMENT '小计金额（分）',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_order_id (order_id),
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单项表';

-- 7. 时长包产品表（可选，用于管理不同的时长包产品）
CREATE TABLE duration_packages (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL COMMENT '产品名称',
    duration_minutes BIGINT NOT NULL COMMENT '时长（分钟）',
    price BIGINT NOT NULL COMMENT '价格（分）',
    status TINYINT DEFAULT 1 COMMENT '状态：1-上架，0-下架',
    sort_order INT DEFAULT 0 COMMENT '排序',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_status (status),
    INDEX idx_sort_order (sort_order)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='时长包产品表';

-- 触发器：订单支付成功后自动增加用户时长
DELIMITER $$
CREATE TRIGGER tr_order_paid_add_duration
AFTER UPDATE ON orders
FOR EACH ROW
BEGIN
    DECLARE total_minutes BIGINT DEFAULT 0;

    -- 当订单状态从未支付变为已支付时
    IF OLD.status != 1 AND NEW.status = 1 THEN
        -- 计算订单中购买的总时长（分钟）
        SELECT SUM(duration_minutes * quantity) INTO total_minutes
        FROM order_items
        WHERE order_id = NEW.id;

        -- 更新用户时长
        INSERT INTO user_duration (user_id, total_duration, remaining_duration)
        VALUES (NEW.user_id, total_minutes * 60, total_minutes * 60)
        ON DUPLICATE KEY UPDATE
            total_duration = total_duration + total_minutes * 60,
            remaining_duration = remaining_duration + total_minutes * 60;
    END IF;
END$$
DELIMITER ;

-- 触发器：转写完成后自动扣减用户时长
DELIMITER $$
CREATE TRIGGER tr_transcription_deduct_duration
AFTER INSERT ON transcription_records
FOR EACH ROW
BEGIN
    -- 扣减用户剩余时长
    UPDATE user_duration
    SET used_duration = used_duration + NEW.duration_used,
        remaining_duration = remaining_duration - NEW.duration_used
    WHERE user_id = NEW.user_id;
END$$
DELIMITER ;

-- 初始化数据：插入一些基础的时长包产品
INSERT INTO duration_packages (name, duration_minutes, price, sort_order) VALUES
('体验包', 30, 999, 1),
('基础包', 120, 2999, 2),
('标准包', 300, 5999, 3),
('专业包', 600, 9999, 4),
('企业包', 1200, 16999, 5);

-- 初始化数据：插入一些测试激活码
INSERT INTO activation_codes (code, type, max_uses, status, expires_at) VALUES
('WELCOME2024', 1, 100, 1, DATE_ADD(NOW(), INTERVAL 1 YEAR)),
('BETA001', 1, 50, 1, DATE_ADD(NOW(), INTERVAL 6 MONTH)),
('INVITE123', 2, 10, 1, DATE_ADD(NOW(), INTERVAL 3 MONTH)),
('TEST001', 1, 1, 1, DATE_ADD(NOW(), INTERVAL 1 MONTH));

-- 创建订单号生成函数（可选）
DELIMITER $$
CREATE FUNCTION generate_order_no() RETURNS VARCHAR(32)
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE order_no VARCHAR(32);
    SET order_no = CONCAT('ORD', DATE_FORMAT(NOW(), '%Y%m%d'), LPAD(FLOOR(RAND() * 999999), 6, '0'));
    RETURN order_no;
END$$
DELIMITER ;

-- 激活码管理表（可选，用于管理和验证激活码）
CREATE TABLE activation_codes (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    code VARCHAR(20) UNIQUE NOT NULL COMMENT '激活码',
    type TINYINT DEFAULT 1 COMMENT '激活码类型：1-注册码，2-邀请码',
    max_uses INT DEFAULT 1 COMMENT '最大使用次数',
    used_count INT DEFAULT 0 COMMENT '已使用次数',
    status TINYINT DEFAULT 1 COMMENT '状态：1-有效，0-无效',
    expires_at TIMESTAMP NULL COMMENT '过期时间',
    created_by BIGINT COMMENT '创建者用户ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_code (code),
    INDEX idx_status (status),
    INDEX idx_expires_at (expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='激活码管理表';

-- 激活码使用记录表
CREATE TABLE activation_code_usage (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    code VARCHAR(20) NOT NULL COMMENT '激活码',
    user_id BIGINT NOT NULL COMMENT '使用者用户ID',
    used_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_code (code),
    INDEX idx_user_id (user_id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='激活码使用记录表';

-- 触发器：用户注册时验证激活码并记录使用
DELIMITER $$
CREATE TRIGGER tr_user_register_check_activation_code
BEFORE INSERT ON users
FOR EACH ROW
BEGIN
    DECLARE code_exists INT DEFAULT 0;
    DECLARE code_valid INT DEFAULT 0;
    DECLARE max_uses INT DEFAULT 0;
    DECLARE used_count INT DEFAULT 0;

    -- 检查激活码是否存在
    SELECT COUNT(*) INTO code_exists
    FROM activation_codes
    WHERE code = NEW.activation_code;

    IF code_exists = 0 THEN
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = '激活码不存在';
    END IF;

    -- 检查激活码是否有效
    SELECT
        CASE
            WHEN status = 1 AND (expires_at IS NULL OR expires_at > NOW()) THEN 1
            ELSE 0
        END,
        max_uses,
        used_count
    INTO code_valid, max_uses, used_count
    FROM activation_codes
    WHERE code = NEW.activation_code;

    IF code_valid = 0 THEN
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = '激活码已失效或过期';
    END IF;

    -- 检查使用次数
    IF used_count >= max_uses THEN
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = '激活码使用次数已达上限';
    END IF;
END$$
DELIMITER ;

-- 触发器：用户注册成功后更新激活码使用次数
DELIMITER $$
CREATE TRIGGER tr_user_register_update_activation_code
AFTER INSERT ON users
FOR EACH ROW
BEGIN
    -- 更新激活码使用次数
    UPDATE activation_codes
    SET used_count = used_count + 1
    WHERE code = NEW.activation_code;

    -- 记录激活码使用记录
    INSERT INTO activation_code_usage (code, user_id)
    VALUES (NEW.activation_code, NEW.id);
END$$
DELIMITER ;

-- 常用查询示例
-- 1. 用户注册（需要在应用层处理密码加密和激活码验证）
-- INSERT INTO users (username, email, password_hash, nickname, activation_code) VALUES (?, ?, ?, ?, ?);
-- INSERT INTO user_duration (user_id, total_duration, remaining_duration) VALUES (LAST_INSERT_ID(), 1800, 1800); -- 新用户赠送30分钟

-- 2. 用户登录验证
-- SELECT id, username, email, password_hash, status FROM users WHERE (username = ? OR email = ?) AND status = 1;

-- 3. 微信登录绑定
-- INSERT INTO user_third_party (user_id, provider, provider_user_id, provider_data) VALUES (?, 'wechat', ?, ?);

-- 4. 查询用户剩余时长
-- SELECT remaining_duration FROM user_duration WHERE user_id = ?;

-- 5. 创建订单
-- INSERT INTO orders (user_id, order_no, total_amount) VALUES (?, generate_order_no(), ?);
-- INSERT INTO order_items (order_id, product_type, product_name, duration_minutes, unit_price, amount) VALUES (?, ?, ?, ?, ?, ?);

-- 6. 查询用户转写历史
-- SELECT * FROM transcription_records WHERE user_id = ? ORDER BY created_at DESC LIMIT 20;

-- 7. 生成激活码
-- INSERT INTO activation_codes (code, type, max_uses, expires_at) VALUES (?, 1, 100, DATE_ADD(NOW(), INTERVAL 1 YEAR));

-- 8. 验证激活码有效性
-- SELECT id, max_uses, used_count, status, expires_at FROM activation_codes WHERE code = ? AND status = 1 AND (expires_at IS NULL OR expires_at > NOW()) AND used_count < max_uses;

-- 9. 查询用户激活码使用记录
-- SELECT acu.*, ac.type FROM activation_code_usage acu JOIN activation_codes ac ON acu.code = ac.code WHERE acu.user_id = ?;
